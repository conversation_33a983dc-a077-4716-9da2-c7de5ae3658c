import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import Modal from '@/components/admin/Modal';
import { toast } from 'react-toastify';
import authTokenManager from '@/lib/auth-token-manager';
import styles from '@/styles/admin/Events.module.css';

/**
 * Admin Events Management Page
 * Manages events and their associated QR codes
 */
export default function AdminEvents() {
  const router = useRouter();
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [filters, setFilters] = useState({
    status: 'all',
    upcoming: false
  });

  useEffect(() => {
    fetchEvents();
  }, [filters]);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const token = authTokenManager.getTokenFromStorage() || await authTokenManager.getAuthToken();

      if (!token) {
        throw new Error('No authentication token available');
      }

      const queryParams = new URLSearchParams({
        status: filters.status,
        upcoming: filters.upcoming.toString(),
        limit: '50'
      });

      const response = await fetch(`/api/admin/events?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch events`);
      }

      const data = await response.json();
      setEvents(data.events || []);
    } catch (error) {
      console.error('Error fetching events:', error);
      toast.error(`Failed to load events: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateEvent = async (eventData) => {
    try {
      const token = authTokenManager.getTokenFromStorage() || await authTokenManager.getAuthToken();

      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch('/api/admin/events', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(eventData)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to create event`);
      }

      const data = await response.json();
      setEvents(prev => [data.event, ...prev]);
      setShowCreateModal(false);
      toast.success('Event created successfully');
    } catch (error) {
      console.error('Error creating event:', error);
      toast.error(`Failed to create event: ${error.message}`);
    }
  };

  const handleViewEvent = (event) => {
    router.push(`/admin/events/${event.id}`);
  };

  const getEventStatus = (event) => {
    const now = new Date();
    const startDate = new Date(event.start_date);
    const endDate = new Date(event.end_date);

    if (now < startDate) return 'upcoming';
    if (now > endDate) return 'completed';
    return 'active';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'upcoming': return '#3498db';
      case 'active': return '#2ecc71';
      case 'completed': return '#95a5a6';
      case 'cancelled': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  return (
    <ProtectedRoute>
      <AdminLayout>
        <div className={styles.container}>
          <div className={styles.header}>
            <div className={styles.titleSection}>
              <h1 className={styles.title}>Event Management</h1>
              <p className={styles.subtitle}>
                Manage events and generate QR codes for mobile bookings
              </p>
            </div>
            <button
              className={styles.createButton}
              onClick={() => setShowCreateModal(true)}
            >
              <span className={styles.buttonIcon}>+</span>
              Create Event
            </button>
          </div>

          <div className={styles.filters}>
            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>Status:</label>
              <select
                className={styles.filterSelect}
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              >
                <option value="all">All Events</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            <div className={styles.filterGroup}>
              <label className={styles.filterCheckbox}>
                <input
                  type="checkbox"
                  checked={filters.upcoming}
                  onChange={(e) => setFilters(prev => ({ ...prev, upcoming: e.target.checked }))}
                />
                <span className={styles.checkboxLabel}>Upcoming Only</span>
              </label>
            </div>
          </div>

          {loading ? (
            <div className={styles.loading}>
              <div className={styles.spinner}></div>
              <p>Loading events...</p>
            </div>
          ) : (
            <div className={styles.eventsGrid}>
              {events.length === 0 ? (
                <div className={styles.emptyState}>
                  <div className={styles.emptyIcon}>📅</div>
                  <h3>No Events Found</h3>
                  <p>Create your first event to start generating QR codes for mobile bookings</p>
                  <button
                    className={styles.createButton}
                    onClick={() => setShowCreateModal(true)}
                  >
                    Create First Event
                  </button>
                </div>
              ) : (
                events.map(event => (
                  <EventCard
                    key={event.id}
                    event={event}
                    status={getEventStatus(event)}
                    statusColor={getStatusColor(getEventStatus(event))}
                    onView={() => handleViewEvent(event)}
                  />
                ))
              )}
            </div>
          )}

          {showCreateModal && (
            <CreateEventModal
              onClose={() => setShowCreateModal(false)}
              onSubmit={handleCreateEvent}
            />
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}

/**
 * Event Card Component
 */
function EventCard({ event, status, statusColor, onView }) {
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-AU', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={styles.eventCard}>
      <div className={styles.cardHeader}>
        <h3 className={styles.eventName}>{event.name}</h3>
        <span
          className={styles.statusBadge}
          style={{ backgroundColor: statusColor }}
        >
          {status}
        </span>
      </div>

      <div className={styles.cardContent}>
        <div className={styles.eventDetail}>
          <span className={styles.detailIcon}>📍</span>
          <span className={styles.detailText}>{event.location}</span>
        </div>

        <div className={styles.eventDetail}>
          <span className={styles.detailIcon}>📅</span>
          <span className={styles.detailText}>
            {formatDate(event.start_date)} - {formatDate(event.end_date)}
          </span>
        </div>

        <div className={styles.eventDetail}>
          <span className={styles.detailIcon}>⏰</span>
          <span className={styles.detailText}>
            {formatTime(event.start_date)} - {formatTime(event.end_date)}
          </span>
        </div>

        {event.max_capacity && (
          <div className={styles.eventDetail}>
            <span className={styles.detailIcon}>👥</span>
            <span className={styles.detailText}>
              {event.current_bookings || 0} / {event.max_capacity} bookings
            </span>
          </div>
        )}

        <div className={styles.eventDetail}>
          <span className={styles.detailIcon}>📱</span>
          <span className={styles.detailText}>
            {event.qr_code_count || 0} QR codes
          </span>
        </div>
      </div>

      <div className={styles.cardActions}>
        <button
          className={styles.viewButton}
          onClick={onView}
        >
          Manage Event
        </button>
      </div>
    </div>
  );
}

/**
 * Create Event Modal Component
 */
function CreateEventModal({ onClose, onSubmit }) {
  const [formData, setFormData] = useState({
    name: '',
    location: '',
    description: '',
    start_date: '',
    end_date: '',
    max_capacity: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <Modal onClose={onClose} title="Create New Event">
      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.formGroup}>
          <label className={styles.label}>Event Name *</label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={styles.input}
            required
            placeholder="e.g., Summer Festival 2024"
          />
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>Location *</label>
          <input
            type="text"
            name="location"
            value={formData.location}
            onChange={handleChange}
            className={styles.input}
            required
            placeholder="e.g., Bondi Beach, Sydney"
          />
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>Description</label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            className={styles.textarea}
            rows="3"
            placeholder="Event description..."
          />
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label className={styles.label}>Start Date & Time *</label>
            <input
              type="datetime-local"
              name="start_date"
              value={formData.start_date}
              onChange={handleChange}
              className={styles.input}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label className={styles.label}>End Date & Time *</label>
            <input
              type="datetime-local"
              name="end_date"
              value={formData.end_date}
              onChange={handleChange}
              className={styles.input}
              required
            />
          </div>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>Maximum Capacity</label>
          <input
            type="number"
            name="max_capacity"
            value={formData.max_capacity}
            onChange={handleChange}
            className={styles.input}
            min="1"
            placeholder="Leave empty for unlimited"
          />
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={onClose}
            className={styles.cancelButton}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.submitButton}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Creating...' : 'Create Event'}
          </button>
        </div>
      </form>
    </Modal>
  );
}
