/* Admin Events Management Styles */

.container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Section */
.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.titleSection {
  flex: 1;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.subtitle {
  font-size: 1rem;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.createButton {
  background: linear-gradient(45deg, #4ECDC4, #44A08D);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(78, 205, 196, 0.3);
}

.createButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
}

.buttonIcon {
  font-size: 1.2rem;
  font-weight: bold;
}

/* Filters Section */
.filters {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  display: flex;
  gap: 2rem;
  align-items: center;
  flex-wrap: wrap;
}

.filterGroup {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filterLabel {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.filterSelect {
  padding: 0.5rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.filterSelect:focus {
  outline: none;
  border-color: #4ECDC4;
}

.filterCheckbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
}

.filterCheckbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #4ECDC4;
}

.checkboxLabel {
  font-weight: 600;
  color: #2c3e50;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Events Grid */
.eventsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Event Card */
.eventCard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e1e8ed;
}

.eventCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.cardHeader {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.eventName {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  line-height: 1.3;
  flex: 1;
}

.statusBadge {
  background: #95a5a6;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.cardContent {
  padding: 0 1.5rem 1rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.eventDetail {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-size: 0.9rem;
}

.detailIcon {
  font-size: 1rem;
  min-width: 20px;
  text-align: center;
}

.detailText {
  color: #555;
  line-height: 1.4;
}

.cardActions {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid #f0f0f0;
}

.viewButton {
  width: 100%;
  background: #4ECDC4;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.viewButton:hover {
  background: #44A08D;
  transform: translateY(-1px);
}

/* Empty State */
.emptyState {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyState h3 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.emptyState p {
  color: #666;
  margin: 0 0 2rem 0;
  max-width: 400px;
  line-height: 1.5;
}

/* Form Styles */
.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.input,
.textarea {
  padding: 0.8rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: #4ECDC4;
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.formActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e8ed;
}

.cancelButton {
  background: #f8f9fa;
  color: #666;
  border: 2px solid #e1e8ed;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancelButton:hover {
  background: #e9ecef;
  border-color: #ced4da;
}

.submitButton {
  background: linear-gradient(45deg, #4ECDC4, #44A08D);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submitButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.submitButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
  }

  .createButton {
    align-self: stretch;
    justify-content: center;
  }

  .filters {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .filterGroup {
    justify-content: space-between;
  }

  .eventsGrid {
    grid-template-columns: 1fr;
  }

  .formRow {
    grid-template-columns: 1fr;
  }

  .formActions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.5rem;
  }

  .eventCard {
    margin: 0 -0.5rem;
  }

  .cardHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .statusBadge {
    align-self: flex-start;
  }
}
